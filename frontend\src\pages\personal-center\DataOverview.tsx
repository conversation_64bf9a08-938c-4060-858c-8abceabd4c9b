import {
  BarChartOutlined,
  CarOutlined,
  UsergroupAddOutlined,
  ExclamationCircleOutlined,
  AlertOutlined,
} from '@ant-design/icons';
import {
  Alert,
  Spin,
  Grid,
  Flex,
  Typography,
} from 'antd';
import { ProCard, StatisticCard } from '@ant-design/pro-components';
import React, { useEffect, useState } from 'react';
import { UserService } from '@/services/user';
import type { UserPersonalStatsResponse } from '@/types/api';

/**
 * 数据概览卡片组件
 *
 * 使用 Ant Design Pro Components 的 StatisticCard 组件显示用户的个人统计数据，
 * 采用单行四列的响应式网格布局。包括车辆、人员、预警、告警等指标的统计卡片。
 *
 * 主要功能：
 * 1. 显示车辆数量统计 - 使用车辆图标，蓝色主题
 * 2. 显示人员数量统计 - 使用用户组图标，绿色主题
 * 3. 显示预警数量统计 - 使用感叹号图标，橙色主题
 * 4. 显示告警数量统计 - 使用警告图标，红色主题
 *
 * 数据来源：
 * - 个人统计数据：通过UserService.getUserPersonalStats()获取
 *
 * 布局特点：
 * - 使用 StatisticCard 组件提供专业的数据展示
 * - 单行四列水平排列，响应式布局适配不同屏幕
 * - 每个统计项都有语义化的图标和颜色主题
 * - 统一的卡片样式和高度
 */
const DataOverview: React.FC = () => {
  /**
   * 响应式检测
   */
  const { useBreakpoint } = Grid;
  const screens = useBreakpoint();

  /**
   * 个人统计数据状态管理
   */
  const [personalStats, setPersonalStats] = useState<UserPersonalStatsResponse>({
    vehicles: 0,
    personnel: 0,
    warnings: 0,
    alerts: 0,
  });

  const [statsLoading, setStatsLoading] = useState(true);
  const [statsError, setStatsError] = useState<string | null>(null);

  // 获取统计数据
  useEffect(() => {
    const fetchStatsData = async () => {
      try {
        const stats = await UserService.getUserPersonalStats();
        setPersonalStats(stats);
        setStatsError(null);
      } catch (error) {
        console.error('获取统计数据失败:', error);
        setStatsError('获取统计数据失败，请稍后重试');
      } finally {
        setStatsLoading(false);
      }
    };

    fetchStatsData();
  }, []);

  return (
    <ProCard
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <BarChartOutlined style={{ fontSize: 16, color: '#1890ff' }} />
          <span>数据概览</span>
        </div>
      }
      style={{
        marginBottom: 16,
        borderRadius: 8,
        border: '1px solid #d9d9d9',
      }}
      headStyle={{
        borderBottom: '1px solid #f0f0f0',
        paddingBottom: 12,
      }}
      bodyStyle={{
        padding: '20px',
      }}
    >
      {statsError ? (
        <Alert
          message="数据概览加载失败"
          description={statsError}
          type="error"
          showIcon
          style={{
            borderRadius: 8,
          }}
        />
      ) : (
        <Spin spinning={statsLoading}>
          {/* 使用 StatisticCard.Group 组件的响应式布局 */}
          <StatisticCard.Group direction={screens?.md ? 'row' : 'column'}>
            {/* 车辆统计 */}
            <StatisticCard
          
            >
              <Flex align="center" justify="flex-start" style={{ height: '100%', padding: '16px 12px' }}>
                {/* 左侧图标 */}
                <CarOutlined style={{ color: '#1890ff', fontSize: 24, marginRight: 16 }} />
                {/* 右侧标题和数值 */}
                <Flex vertical align="flex-start" justify="center" style={{ flex: 1 }}>
                  <Typography.Text style={{ fontSize: 14, color: '#666', marginBottom: 4 }}>
                    车辆
                  </Typography.Text>
                  <Typography.Text style={{
                    color: '#1890ff',
                    fontSize: 32,
                    fontWeight: 700,
                    lineHeight: 1,
                  }}>
                    {personalStats.vehicles}
                  </Typography.Text>
                </Flex>
              </Flex>
            </StatisticCard>

            {/* 人员统计 */}
            <StatisticCard
         
            >
              <Flex align="center" justify="flex-start" style={{ height: '100%', padding: '16px 12px' }}>
                {/* 左侧图标 */}
                <UsergroupAddOutlined style={{ color: '#52c41a', fontSize: 24, marginRight: 16 }} />
                {/* 右侧标题和数值 */}
                <Flex vertical align="flex-start" justify="center" style={{ flex: 1 }}>
                  <Typography.Text style={{ fontSize: 14, color: '#666', marginBottom: 4 }}>
                    人员
                  </Typography.Text>
                  <Typography.Text style={{
                    color: '#52c41a',
                    fontSize: 32,
                    fontWeight: 700,
                    lineHeight: 1,
                  }}>
                    {personalStats.personnel}
                  </Typography.Text>
                </Flex>
              </Flex>
            </StatisticCard>

            {/* 预警统计 */}
            <StatisticCard
             
            >
              <Flex align="center" justify="flex-start" style={{ height: '100%', padding: '16px 12px' }}>
                {/* 左侧图标 */}
                <ExclamationCircleOutlined style={{ color: '#faad14', fontSize: 24, marginRight: 16 }} />
                {/* 右侧标题和数值 */}
                <Flex vertical align="flex-start" justify="center" style={{ flex: 1 }}>
                  <Typography.Text style={{ fontSize: 14, color: '#666', marginBottom: 4 }}>
                    预警
                  </Typography.Text>
                  <Typography.Text style={{
                    color: '#faad14',
                    fontSize: 32,
                    fontWeight: 700,
                    lineHeight: 1,
                  }}>
                    {personalStats.warnings}
                  </Typography.Text>
                </Flex>
              </Flex>
            </StatisticCard>

            {/* 告警统计 */}
            <StatisticCard
          
            >
              <Flex align="center" justify="flex-start" style={{ height: '100%', padding: '16px 12px' }}>
                {/* 左侧图标 */}
                <AlertOutlined style={{ color: '#ff4d4f', fontSize: 24, marginRight: 16 }} />
                {/* 右侧标题和数值 */}
                <Flex vertical align="flex-start" justify="center" style={{ flex: 1 }}>
                  <Typography.Text style={{ fontSize: 14, color: '#666', marginBottom: 4 }}>
                    告警
                  </Typography.Text>
                  <Typography.Text style={{
                    color: '#ff4d4f',
                    fontSize: 32,
                    fontWeight: 700,
                    lineHeight: 1,
                  }}>
                    {personalStats.alerts}
                  </Typography.Text>
                </Flex>
              </Flex>
            </StatisticCard>
          </StatisticCard.Group>
        </Spin>
      )}
    </ProCard>
  );
};

export default DataOverview;
