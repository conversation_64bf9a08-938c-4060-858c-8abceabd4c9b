{"version": 3, "sources": ["src_pages_personal-center_index_tsx-async.1021708354673871669.hot-update.js", "src/pages/personal-center/TodoManagement.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/personal-center/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='10566647384502541319';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"src/pages/personal-center/index.tsx\"],\"src/pages/settings/index.tsx\":[\"p__settings__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "import {\n  CalendarOutlined,\n  CheckOutlined,\n  DeleteOutlined,\n  EditOutlined,\n  MoreOutlined,\n  PlusOutlined,\n  SearchOutlined,\n} from '@ant-design/icons';\nimport {\n  Alert,\n  Button,\n  Col,\n  Dropdown,\n  Flex,\n  Form,\n  Input,\n  Progress,\n  Row,\n  Select,\n  Space,\n  Spin,\n  Tabs,\n  Tooltip,\n  Typography,\n} from 'antd';\nimport { ModalForm, ProList, ProCard } from '@ant-design/pro-components';\nimport React, { useEffect, useState, useMemo } from 'react';\nimport { TodoService } from '@/services/todo';\nimport type { TodoResponse, TodoStatsResponse } from '@/types/api';\nimport { usePagination } from '@/utils/paginationUtils';\n\nconst { Text } = Typography;\nconst { TabPane } = Tabs;\n\n// 使用API类型定义，不需要重复定义接口\ninterface TodoManagementProps {\n  onAddTodo?: (todo: TodoResponse) => void;\n  onUpdateTodo?: (id: number, updatedTodo: Partial<TodoResponse>) => void;\n  onDeleteTodo?: (id: number) => void;\n}\n\nconst TodoManagement: React.FC<TodoManagementProps> = () => {\n  // TODO数据状态管理\n  const [personalTasks, setPersonalTasks] = useState<TodoResponse[]>([]);\n  const [todoStats, setTodoStats] = useState<TodoStatsResponse>({\n    highPriorityCount: 0,\n    mediumPriorityCount: 0,\n    lowPriorityCount: 0,\n    totalCount: 0,\n    completedCount: 0,\n    completionPercentage: 0,\n  });\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // 待办事项状态管理\n  const [todoModalVisible, setTodoModalVisible] = useState(false);\n  const [todoForm] = Form.useForm();\n  const [editingTodoId, setEditingTodoId] = useState<number | null>(null);\n\n  // 过滤器状态\n  const [activeTab, setActiveTab] = useState<'all' | 'pending' | 'completed'>(\n    'pending',\n  );\n  const [searchText, setSearchText] = useState('');\n\n  // 分页功能\n  const { pagination, updateTotal } = usePagination({\n    defaultPageSize: 10,\n    pageSizeOptions: ['5', '10', '20', '50'],\n    showTotal: (total, range) => `共 ${total} 条待办事项，显示第 ${range[0]}-${range[1]} 条`,\n  });\n\n  // 获取TODO数据\n  useEffect(() => {\n    const fetchTodoData = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n\n        console.log('TodoManagement: 开始获取TODO数据');\n\n        // 分别获取TODO列表和统计数据，避免一个失败影响另一个\n        const todosPromise = TodoService.getUserTodos().catch((error) => {\n          console.error('获取TODO列表失败:', error);\n          return [];\n        });\n\n        const statsPromise = TodoService.getTodoStats().catch((error) => {\n          console.error('获取TODO统计失败:', error);\n          return {\n            highPriorityCount: 0,\n            mediumPriorityCount: 0,\n            lowPriorityCount: 0,\n            totalCount: 0,\n            completedCount: 0,\n            completionPercentage: 0,\n          };\n        });\n\n        const [todos, stats] = await Promise.all([todosPromise, statsPromise]);\n\n        console.log('TodoManagement: 获取到TODO列表:', todos);\n        console.log('TodoManagement: 获取到统计数据:', stats);\n\n        setPersonalTasks(todos);\n        setTodoStats(stats);\n      } catch (error) {\n        console.error('获取TODO数据时发生未知错误:', error);\n        setError('获取TODO数据失败，请刷新页面重试');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchTodoData();\n  }, []);\n\n  // 根据激活的标签和搜索文本过滤任务\n  const filteredPersonalTasks = useMemo(() => {\n    return (personalTasks || []).filter((task) => {\n      // 根据标签过滤\n      if (activeTab === 'pending' && task.status === 1) return false;\n      if (activeTab === 'completed' && task.status === 0) return false;\n\n      // 根据搜索文本过滤\n      if (\n        searchText &&\n        !task.title.toLowerCase().includes(searchText.toLowerCase())\n      ) {\n        return false;\n      }\n\n      return true;\n    });\n  }, [personalTasks, activeTab, searchText]);\n\n  // 更新总数\n  React.useEffect(() => {\n    updateTotal(filteredPersonalTasks.length);\n  }, [filteredPersonalTasks.length, updateTotal]);\n\n  // 处理待办事项操作\n  const handleToggleTodoStatus = async (id: number) => {\n    try {\n      const task = personalTasks.find((t) => t.id === id);\n      if (!task) {\n        return;\n      }\n\n      const newStatus = task.status === 0 ? 1 : 0;\n\n      await TodoService.updateTodo(id, { status: newStatus });\n\n      // 更新本地状态\n      setPersonalTasks(\n        personalTasks.map((task) =>\n          task.id === id ? { ...task, status: newStatus } : task,\n        ),\n      );\n\n      // 刷新统计数据\n      try {\n        const stats = await TodoService.getTodoStats();\n        setTodoStats(stats);\n      } catch (statsError) {\n        // 统计数据刷新失败不影响主要操作\n      }\n    } catch (error) {\n      // 错误处理由响应拦截器统一处理\n    }\n  };\n\n  const handleAddOrUpdateTodo = async (values: any) => {\n    try {\n      if (editingTodoId) {\n        // 更新现有待办事项\n        const updatedTodo = await TodoService.updateTodo(editingTodoId, {\n          title: values.name,\n          priority: values.priority,\n        });\n\n        setPersonalTasks(\n          personalTasks.map((task) =>\n            task.id === editingTodoId ? updatedTodo : task,\n          ),\n        );\n      } else {\n        // 添加新待办事项\n        const newTodo = await TodoService.createTodo({\n          title: values.name,\n          priority: values.priority,\n        });\n\n        setPersonalTasks([newTodo, ...personalTasks]);\n      }\n\n      // 刷新统计数据\n      try {\n        const stats = await TodoService.getTodoStats();\n        setTodoStats(stats);\n      } catch (statsError) {\n        // 统计数据刷新失败不影响主要操作\n      }\n\n      // 重置表单并关闭模态框\n      setTodoModalVisible(false);\n      setEditingTodoId(null);\n      todoForm.resetFields();\n    } catch (error) {\n      // 错误处理由响应拦截器统一处理\n    }\n  };\n\n  const handleDeleteTodo = async (id: number) => {\n    try {\n      await TodoService.deleteTodo(id);\n      setPersonalTasks(personalTasks.filter((task) => task.id !== id));\n\n      // 刷新统计数据\n      try {\n        const stats = await TodoService.getTodoStats();\n        setTodoStats(stats);\n      } catch (statsError) {\n        // 统计数据刷新失败不影响主要操作\n      }\n    } catch (error) {\n      // 错误处理由响应拦截器统一处理\n    }\n  };\n\n  return (\n    <ProCard\n      title=\"待办事项/任务列表\"\n      style={{\n        borderRadius: 8,\n        height: 'fit-content',\n        minHeight: '600px', // 确保右列有足够的高度\n      }}\n      headStyle={{\n        borderBottom: '1px solid #f0f0f0',\n        paddingBottom: 12,\n      }}\n      bodyStyle={{\n        padding: '16px',\n      }}\n    >\n      {/* 任务管理界面头部区域 */}\n      <div\n        style={{\n          marginBottom: 16,\n          padding: '12px 16px',\n          background: '#fafbfc',\n          borderRadius: 8,\n          border: '1px solid #f0f0f0',\n        }}\n      >\n        {/* 全新优化的统计信息区域 */}\n        <div style={{\n          marginBottom: 20,\n          padding: '20px',\n          background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',\n          borderRadius: 16,\n          border: '1px solid #e2e8f0',\n          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'\n        }}>\n          {/* 标题区域 */}\n          <div style={{ marginBottom: 20 }}>\n            <Flex align=\"center\" gap={10}>\n              <CheckOutlined style={{ color: '#1890ff', fontSize: 18 }} />\n              <Text strong style={{ fontSize: 18, color: '#1f2937' }}>\n                任务统计概览\n              </Text>\n            </Flex>\n          </div>\n\n          <Row gutter={[20, 16]}>\n            {/* 优先级统计卡片组 */}\n            <Col xs={24} sm={24} md={16} lg={16} xl={18}>\n              <Row gutter={[12, 12]}>\n                {/* 高优先级卡片 */}\n                <Col xs={24} sm={8} md={8} lg={8} xl={8}>\n                  <div\n                    style={{\n                      background: 'linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%)',\n                      border: '2px solid #fca5a5',\n                      borderRadius: 12,\n                      padding: '16px',\n                      textAlign: 'center',\n                      position: 'relative',\n                      overflow: 'hidden',\n                      boxShadow: '0 2px 4px rgba(239, 68, 68, 0.1)',\n                      transition: 'all 0.3s ease'\n                    }}\n                  >\n                    {/* 装饰性背景 */}\n                    <div style={{\n                      position: 'absolute',\n                      top: -20,\n                      right: -20,\n                      width: 40,\n                      height: 40,\n                      background: 'rgba(239, 68, 68, 0.1)',\n                      borderRadius: '50%'\n                    }} />\n\n                    <Flex vertical align=\"center\" gap={8}>\n                      <div\n                        style={{\n                          width: 12,\n                          height: 12,\n                          borderRadius: '50%',\n                          background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                          boxShadow: '0 2px 4px rgba(239, 68, 68, 0.3)'\n                        }}\n                      />\n                      <Text style={{ fontSize: 12, color: '#6b7280', fontWeight: 500 }}>\n                        高优先级\n                      </Text>\n                      <Text style={{\n                        fontSize: 24,\n                        fontWeight: 700,\n                        color: '#dc2626',\n                        lineHeight: 1\n                      }}>\n                        {todoStats.highPriorityCount}\n                      </Text>\n                    </Flex>\n                  </div>\n                </Col>\n\n                {/* 中优先级卡片 */}\n                <Col xs={24} sm={8} md={8} lg={8} xl={8}>\n                  <div\n                    style={{\n                      background: 'linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%)',\n                      border: '2px solid #fcd34d',\n                      borderRadius: 12,\n                      padding: '16px',\n                      textAlign: 'center',\n                      position: 'relative',\n                      overflow: 'hidden',\n                      boxShadow: '0 2px 4px rgba(245, 158, 11, 0.1)',\n                      transition: 'all 0.3s ease'\n                    }}\n                  >\n                    {/* 装饰性背景 */}\n                    <div style={{\n                      position: 'absolute',\n                      top: -20,\n                      right: -20,\n                      width: 40,\n                      height: 40,\n                      background: 'rgba(245, 158, 11, 0.1)',\n                      borderRadius: '50%'\n                    }} />\n\n                    <Flex vertical align=\"center\" gap={8}>\n                      <div\n                        style={{\n                          width: 12,\n                          height: 12,\n                          borderRadius: '50%',\n                          background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n                          boxShadow: '0 2px 4px rgba(245, 158, 11, 0.3)'\n                        }}\n                      />\n                      <Text style={{ fontSize: 12, color: '#6b7280', fontWeight: 500 }}>\n                        中优先级\n                      </Text>\n                      <Text style={{\n                        fontSize: 24,\n                        fontWeight: 700,\n                        color: '#d97706',\n                        lineHeight: 1\n                      }}>\n                        {todoStats.mediumPriorityCount}\n                      </Text>\n                    </Flex>\n                  </div>\n                </Col>\n\n                {/* 低优先级卡片 */}\n                <Col xs={24} sm={8} md={8} lg={8} xl={8}>\n                  <div\n                    style={{\n                      background: 'linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%)',\n                      border: '2px solid #d1d5db',\n                      borderRadius: 12,\n                      padding: '16px',\n                      textAlign: 'center',\n                      position: 'relative',\n                      overflow: 'hidden',\n                      boxShadow: '0 2px 4px rgba(107, 114, 128, 0.1)',\n                      transition: 'all 0.3s ease'\n                    }}\n                  >\n                    {/* 装饰性背景 */}\n                    <div style={{\n                      position: 'absolute',\n                      top: -20,\n                      right: -20,\n                      width: 40,\n                      height: 40,\n                      background: 'rgba(107, 114, 128, 0.1)',\n                      borderRadius: '50%'\n                    }} />\n\n                    <Flex vertical align=\"center\" gap={8}>\n                      <div\n                        style={{\n                          width: 12,\n                          height: 12,\n                          borderRadius: '50%',\n                          background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n                          boxShadow: '0 2px 4px rgba(107, 114, 128, 0.3)'\n                        }}\n                      />\n                      <Text style={{ fontSize: 12, color: '#6b7280', fontWeight: 500 }}>\n                        低优先级\n                      </Text>\n                      <Text style={{\n                        fontSize: 24,\n                        fontWeight: 700,\n                        color: '#4b5563',\n                        lineHeight: 1\n                      }}>\n                        {todoStats.lowPriorityCount}\n                      </Text>\n                    </Flex>\n                  </div>\n                </Col>\n              </Row>\n            </Col>\n\n            {/* 完成率可视化卡片 */}\n            <Col xs={24} sm={24} md={8} lg={8} xl={6}>\n              <div\n                style={{\n                  background: 'linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%)',\n                  border: '2px solid #86efac',\n                  borderRadius: 12,\n                  padding: '20px',\n                  height: '100%',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  position: 'relative',\n                  overflow: 'hidden',\n                  boxShadow: '0 4px 6px rgba(34, 197, 94, 0.1)',\n                  minHeight: '140px'\n                }}\n              >\n                {/* 装饰性背景 */}\n                <div style={{\n                  position: 'absolute',\n                  top: -30,\n                  right: -30,\n                  width: 60,\n                  height: 60,\n                  background: 'rgba(34, 197, 94, 0.1)',\n                  borderRadius: '50%'\n                }} />\n\n                <Tooltip\n                  title={`完成情况: ${todoStats.completedCount}/${todoStats.totalCount} 个任务已完成`}\n                >\n                  <Flex vertical align=\"center\" gap={12}>\n                    <Text style={{ fontSize: 14, color: '#6b7280', fontWeight: 500 }}>\n                      任务完成率\n                    </Text>\n\n                    {/* 环形进度条 */}\n                    <Progress\n                      type=\"circle\"\n                      percent={todoStats.completionPercentage}\n                      size={80}\n                      strokeColor={{\n                        '0%': '#10b981',\n                        '100%': '#059669',\n                      }}\n                      trailColor=\"#e5e7eb\"\n                      strokeWidth={8}\n                      format={(percent) => (\n                        <Text style={{\n                          fontSize: 16,\n                          fontWeight: 700,\n                          color: '#059669'\n                        }}>\n                          {percent}%\n                        </Text>\n                      )}\n                    />\n\n                    <Text style={{ fontSize: 12, color: '#6b7280', textAlign: 'center' }}>\n                      {todoStats.completedCount}/{todoStats.totalCount} 已完成\n                    </Text>\n                  </Flex>\n                </Tooltip>\n              </div>\n            </Col>\n          </Row>\n        </div>\n\n        {/* 优化的搜索和操作区域 */}\n        <div style={{\n          marginBottom: 20,\n          padding: '16px',\n          background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',\n          borderRadius: 12,\n          border: '1px solid #e2e8f0',\n          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)'\n        }}>\n          <Row gutter={[16, 12]} align=\"middle\">\n            <Col xs={24} sm={24} md={16} lg={18} xl={20}>\n              <div style={{ position: 'relative' }}>\n                <Input.Search\n                  placeholder=\"搜索任务标题、描述或标签...\"\n                  allowClear\n                  prefix={<SearchOutlined style={{ color: '#6b7280' }} />}\n                  value={searchText}\n                  onChange={(e) => setSearchText(e.target.value)}\n                  style={{\n                    width: '100%',\n                    borderRadius: 8,\n                    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n                  }}\n                  size=\"large\"\n                />\n                {/* 搜索提示 */}\n                {searchText.trim() && (\n                  <div style={{\n                    position: 'absolute',\n                    top: '100%',\n                    left: 0,\n                    right: 0,\n                    marginTop: 4,\n                    padding: '6px 12px',\n                    background: '#ffffff',\n                    border: '1px solid #e2e8f0',\n                    borderRadius: 6,\n                    fontSize: 12,\n                    color: '#6b7280',\n                    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',\n                    zIndex: 10\n                  }}>\n                    <Flex align=\"center\" gap={6}>\n                      <SearchOutlined style={{ fontSize: 12 }} />\n                      <Text style={{ fontSize: 12 }}>\n                        正在搜索: \"{searchText}\"\n                      </Text>\n                    </Flex>\n                  </div>\n                )}\n              </div>\n            </Col>\n\n            <Col xs={24} sm={24} md={8} lg={6} xl={4}>\n              <Button\n                type=\"primary\"\n                icon={<PlusOutlined />}\n                onClick={() => {\n                  setEditingTodoId(null);\n                  todoForm.resetFields();\n                  setTodoModalVisible(true);\n                }}\n                size=\"large\"\n                style={{\n                  width: '100%',\n                  borderRadius: 8,\n                  background: 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',\n                  border: 'none',\n                  boxShadow: '0 2px 4px rgba(24, 144, 255, 0.3)',\n                  fontWeight: 500\n                }}\n              >\n                新建任务\n              </Button>\n            </Col>\n          </Row>\n        </div>\n      </div>\n\n      {/* 优化的标签页 */}\n      <div style={{ marginBottom: 16 }}>\n        <Tabs\n          activeKey={activeTab}\n          onChange={(key) => setActiveTab(key as 'all' | 'pending' | 'completed')}\n          size=\"large\"\n          style={{\n            background: '#ffffff',\n            borderRadius: 8,\n            padding: '0 16px',\n            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n          }}\n          tabBarStyle={{\n            marginBottom: 0,\n            borderBottom: '2px solid #f0f0f0'\n          }}\n        >\n          <TabPane\n            tab={\n              <Flex align=\"center\" gap={6}>\n                <UnorderedListOutlined />\n                <span>全部任务</span>\n              </Flex>\n            }\n            key=\"all\"\n          />\n          <TabPane\n            tab={\n              <Flex align=\"center\" gap={6}>\n                <ClockCircleOutlined />\n                <span>待处理</span>\n              </Flex>\n            }\n            key=\"pending\"\n          />\n          <TabPane\n            tab={\n              <Flex align=\"center\" gap={6}>\n                <CheckCircleOutlined />\n                <span>已完成</span>\n              </Flex>\n            }\n            key=\"completed\"\n          />\n        </Tabs>\n      </div>\n\n      {/* 优化的搜索结果统计 */}\n      {personalTasks.length > 0 && !loading && !error && (\n        <div style={{\n          marginBottom: 16,\n          padding: '12px 16px',\n          background: searchText.trim()\n            ? (filteredPersonalTasks.length > 0\n                ? 'linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%)'\n                : 'linear-gradient(135deg, #fff2f0 0%, #ffccc7 100%)')\n            : 'linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%)',\n          borderRadius: 8,\n          border: searchText.trim()\n            ? (filteredPersonalTasks.length > 0\n                ? '1px solid #91d5ff'\n                : '1px solid #ff7875')\n            : '1px solid #95de64',\n          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)'\n        }}>\n          <Flex align=\"center\" gap={8}>\n            {searchText.trim() ? (\n              filteredPersonalTasks.length > 0 ? (\n                <>\n                  <CheckCircleOutlined style={{ color: '#1890ff', fontSize: 16 }} />\n                  <Text style={{ fontSize: 14, color: '#1890ff' }}>\n                    找到 <Text strong style={{ color: '#1890ff', fontSize: 16 }}>{filteredPersonalTasks.length}</Text> 条匹配的待办事项\n                  </Text>\n                </>\n              ) : (\n                <>\n                  <ExclamationCircleOutlined style={{ color: '#ff4d4f', fontSize: 16 }} />\n                  <Text style={{ fontSize: 14, color: '#ff4d4f' }}>\n                    未找到匹配的待办事项，请尝试其他关键词\n                  </Text>\n                </>\n              )\n            ) : (\n              <>\n                <UnorderedListOutlined style={{ color: '#52c41a', fontSize: 16 }} />\n                <Text style={{ fontSize: 14, color: '#52c41a' }}>\n                  共有 <Text strong style={{ color: '#52c41a', fontSize: 16 }}>{filteredPersonalTasks.length}</Text> 条待办事项\n                </Text>\n              </>\n            )}\n          </Flex>\n        </div>\n      )}\n\n      {/* 待办事项列表 */}\n      {error ? (\n        <Alert\n          message=\"TODO数据加载失败\"\n          description={error}\n          type=\"error\"\n          showIcon\n          style={{ marginBottom: 16 }}\n        />\n      ) : (\n        <Spin spinning={loading}>\n          <ProList\n            dataSource={filteredPersonalTasks}\n            pagination={{\n              current: pagination.current,\n              pageSize: pagination.pageSize,\n              total: filteredPersonalTasks.length,\n              showSizeChanger: pagination.showSizeChanger,\n              showQuickJumper: pagination.showQuickJumper,\n              showTotal: pagination.showTotal,\n              pageSizeOptions: pagination.pageSizeOptions,\n              onChange: pagination.onChange,\n              onShowSizeChange: pagination.onShowSizeChange,\n            }}\n            renderItem={(item) => {\n              return (\n                <div\n                  className=\"todo-item\"\n                  style={{\n                    padding: '10px 16px',\n                    marginBottom: 12,\n                    borderRadius: 8,\n                    background: '#fff',\n                    opacity: item.status === 1 ? 0.7 : 1,\n                    borderLeft: `3px solid ${\n                      item.status === 1\n                        ? '#52c41a'\n                        : item.priority === 3\n                          ? '#ff4d4f'\n                          : item.priority === 2\n                            ? '#faad14'\n                            : '#8c8c8c'\n                    }`,\n                    boxShadow: '0 1px 4px rgba(0,0,0,0.05)',\n                  }}\n                >\n                  <Flex align=\"center\" gap={12} style={{ width: '100%' }}>\n                    {/* 左侧状态和优先级指示器 */}\n                    <Flex vertical align=\"center\">\n                      {item.status === 1 ? (\n                        <Flex\n                          align=\"center\"\n                          justify=\"center\"\n                          style={{\n                            width: 22,\n                            height: 22,\n                            borderRadius: '50%',\n                            background: '#52c41a',\n                          }}\n                        >\n                          <CheckOutlined\n                            style={{ color: '#fff', fontSize: 12 }}\n                          />\n                        </Flex>\n                      ) : (\n                        <div\n                          style={{\n                            width: 18,\n                            height: 18,\n                            borderRadius: '50%',\n                            border: `2px solid ${\n                              item.priority === 3\n                                ? '#ff4d4f'\n                                : item.priority === 2\n                                  ? '#faad14'\n                                  : '#8c8c8c'\n                            }`,\n                          }}\n                        />\n                      )}\n\n                      <div\n                        style={{\n                          width: 2,\n                          height: 24,\n                          background: '#f0f0f0',\n                          marginTop: 4,\n                        }}\n                      />\n                    </Flex>\n\n                    {/* 任务信息区 */}\n                    <Flex vertical style={{ flex: 1 }}>\n                      <Text\n                        style={{\n                          fontSize: 14,\n                          fontWeight: item.priority === 3 ? 500 : 'normal',\n                          textDecoration:\n                            item.status === 1 ? 'line-through' : 'none',\n                          color: item.status === 1 ? '#8c8c8c' : '#262626',\n                        }}\n                      >\n                        {item.title}\n                      </Text>\n\n                      {/* 显示创建日期 */}\n                      <Space align=\"center\" size={6} style={{ marginTop: 4 }}>\n                        <CalendarOutlined\n                          style={{\n                            fontSize: 12,\n                            color: '#8c8c8c',\n                          }}\n                        />\n                        <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                          创建于:{' '}\n                          {new Date(item.createdAt).toLocaleDateString('zh-CN')}\n                        </Text>\n                      </Space>\n                    </Flex>\n\n                    {/* 操作按钮区 */}\n                    <Dropdown\n                      trigger={['click']}\n                      menu={{\n                        items: [\n                          {\n                            key: 'complete',\n                            label:\n                              item.status === 1 ? '标记未完成' : '标记完成',\n                            icon: (\n                              <CheckOutlined\n                                style={{\n                                  color:\n                                    item.status === 1 ? '#8c8c8c' : '#52c41a',\n                                  fontSize: 14,\n                                }}\n                              />\n                            ),\n                          },\n                          {\n                            key: 'edit',\n                            label: '编辑任务',\n                            icon: <EditOutlined style={{ color: '#8c8c8c' }} />,\n                          },\n                          {\n                            key: 'delete',\n                            label: '删除任务',\n                            icon: (\n                              <DeleteOutlined style={{ color: '#ff4d4f' }} />\n                            ),\n                            danger: true,\n                          },\n                        ],\n                        onClick: ({ key }) => {\n                          if (key === 'complete') {\n                            handleToggleTodoStatus(item.id);\n                          } else if (key === 'edit') {\n                            setEditingTodoId(item.id);\n                            todoForm.setFieldsValue({\n                              name: item.title,\n                              priority: item.priority,\n                            });\n                            setTodoModalVisible(true);\n                          } else if (key === 'delete') {\n                            handleDeleteTodo(item.id);\n                          }\n                        },\n                      }}\n                    >\n                      <Button\n                        type=\"text\"\n                        size=\"small\"\n                        icon={<MoreOutlined />}\n                        style={{ width: 32, height: 32 }}\n                      />\n                    </Dropdown>\n                  </Flex>\n                </div>\n              );\n            }}\n          />\n\n          {/* 待办事项表单模态框 */}\n          <ModalForm\n            title={editingTodoId ? '编辑待办事项' : '新增待办事项'}\n            open={todoModalVisible}\n            onOpenChange={(visible) => {\n              setTodoModalVisible(visible);\n              if (!visible) {\n                setEditingTodoId(null);\n                todoForm.resetFields();\n              }\n            }}\n            form={todoForm}\n            layout=\"vertical\"\n            onFinish={handleAddOrUpdateTodo}\n            autoComplete=\"off\"\n            width={500}\n            modalProps={{\n              centered: true,\n              destroyOnClose: true,\n              maskClosable: true,\n              keyboard: true,\n              forceRender: false,\n            }}\n            submitter={{\n              searchConfig: {\n                submitText: editingTodoId ? '更新任务' : '创建任务',\n                resetText: '取消',\n              },\n              submitButtonProps: {\n                style: {\n                  background: '#1890ff',\n                  borderColor: '#1890ff',\n                  boxShadow: '0 2px 4px rgba(24, 144, 255, 0.3)',\n                },\n                icon: editingTodoId ? <EditOutlined /> : <PlusOutlined />,\n              },\n              resetButtonProps: {\n                style: {\n                  borderColor: '#d9d9d9',\n                },\n              },\n              onReset: () => {\n                setTodoModalVisible(false);\n                setEditingTodoId(null);\n                todoForm.resetFields();\n              },\n            }}\n            preserve={false}\n          >\n            <Form.Item\n              name=\"name\"\n              label=\"任务名称\"\n              rules={[{ required: true, message: '请输入任务名称' }]}\n            >\n              <Input\n                placeholder=\"请输入任务名称\"\n                size=\"large\"\n                style={{ borderRadius: 6 }}\n              />\n            </Form.Item>\n\n            <Form.Item\n              name=\"priority\"\n              label=\"优先级\"\n              initialValue={2}\n              rules={[{ required: true, message: '请选择优先级' }]}\n            >\n              <Select\n                size=\"large\"\n                options={[\n                  { value: 3, label: '高优先级' },\n                  { value: 2, label: '中优先级' },\n                  { value: 1, label: '低优先级' },\n                ]}\n                style={{ borderRadius: 6 }}\n              />\n            </Form.Item>\n          </ModalForm>\n        </Spin>\n      )}\n    </ProCard>\n  );\n};\n\nexport default TodoManagement;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uCACA;IACE,SAAS;;;;;;wCC86Bb;;;2BAAA;;;;;;0CAz6BO;yCAiBA;kDACqC;oFACQ;yCACxB;oDAEE;;;;;;;;;;YAE9B,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;YAC3B,MAAM,EAAE,OAAO,EAAE,GAAG,UAAI;YASxB,MAAM,iBAAgD;;gBACpD,aAAa;gBACb,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAiB,EAAE;gBACrE,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAoB;oBAC5D,mBAAmB;oBACnB,qBAAqB;oBACrB,kBAAkB;oBAClB,YAAY;oBACZ,gBAAgB;oBAChB,sBAAsB;gBACxB;gBACA,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;gBACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAgB;gBAElD,WAAW;gBACX,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAC;gBACzD,MAAM,CAAC,SAAS,GAAG,UAAI,CAAC,OAAO;gBAC/B,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAgB;gBAElE,QAAQ;gBACR,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EACxC;gBAEF,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;gBAE7C,OAAO;gBACP,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,GAAG,IAAA,8BAAa,EAAC;oBAChD,iBAAiB;oBACjB,iBAAiB;wBAAC;wBAAK;wBAAM;wBAAM;qBAAK;oBACxC,WAAW,CAAC,OAAO,QAAU,CAAC,EAAE,EAAE,MAAM,WAAW,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;gBAC/E;gBAEA,WAAW;gBACX,IAAA,gBAAS,EAAC;oBACR,MAAM,gBAAgB;wBACpB,IAAI;4BACF,WAAW;4BACX,SAAS;4BAET,QAAQ,GAAG,CAAC;4BAEZ,8BAA8B;4BAC9B,MAAM,eAAe,iBAAW,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC;gCACrD,QAAQ,KAAK,CAAC,eAAe;gCAC7B,OAAO,EAAE;4BACX;4BAEA,MAAM,eAAe,iBAAW,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC;gCACrD,QAAQ,KAAK,CAAC,eAAe;gCAC7B,OAAO;oCACL,mBAAmB;oCACnB,qBAAqB;oCACrB,kBAAkB;oCAClB,YAAY;oCACZ,gBAAgB;oCAChB,sBAAsB;gCACxB;4BACF;4BAEA,MAAM,CAAC,OAAO,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;gCAAC;gCAAc;6BAAa;4BAErE,QAAQ,GAAG,CAAC,8BAA8B;4BAC1C,QAAQ,GAAG,CAAC,4BAA4B;4BAExC,iBAAiB;4BACjB,aAAa;wBACf,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,oBAAoB;4BAClC,SAAS;wBACX,SAAU;4BACR,WAAW;wBACb;oBACF;oBAEA;gBACF,GAAG,EAAE;gBAEL,mBAAmB;gBACnB,MAAM,wBAAwB,IAAA,cAAO,EAAC;oBACpC,OAAO,AAAC,CAAA,iBAAiB,EAAE,AAAD,EAAG,MAAM,CAAC,CAAC;wBACnC,SAAS;wBACT,IAAI,cAAc,aAAa,KAAK,MAAM,KAAK,GAAG,OAAO;wBACzD,IAAI,cAAc,eAAe,KAAK,MAAM,KAAK,GAAG,OAAO;wBAE3D,WAAW;wBACX,IACE,cACA,CAAC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,KAEzD,OAAO;wBAGT,OAAO;oBACT;gBACF,GAAG;oBAAC;oBAAe;oBAAW;iBAAW;gBAEzC,OAAO;gBACP,cAAK,CAAC,SAAS,CAAC;oBACd,YAAY,sBAAsB,MAAM;gBAC1C,GAAG;oBAAC,sBAAsB,MAAM;oBAAE;iBAAY;gBAE9C,WAAW;gBACX,MAAM,yBAAyB,OAAO;oBACpC,IAAI;wBACF,MAAM,OAAO,cAAc,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;wBAChD,IAAI,CAAC,MACH;wBAGF,MAAM,YAAY,KAAK,MAAM,KAAK,IAAI,IAAI;wBAE1C,MAAM,iBAAW,CAAC,UAAU,CAAC,IAAI;4BAAE,QAAQ;wBAAU;wBAErD,SAAS;wBACT,iBACE,cAAc,GAAG,CAAC,CAAC,OACjB,KAAK,EAAE,KAAK,KAAK;gCAAE,GAAG,IAAI;gCAAE,QAAQ;4BAAU,IAAI;wBAItD,SAAS;wBACT,IAAI;4BACF,MAAM,QAAQ,MAAM,iBAAW,CAAC,YAAY;4BAC5C,aAAa;wBACf,EAAE,OAAO,YAAY;wBACnB,kBAAkB;wBACpB;oBACF,EAAE,OAAO,OAAO;oBACd,iBAAiB;oBACnB;gBACF;gBAEA,MAAM,wBAAwB,OAAO;oBACnC,IAAI;wBACF,IAAI,eAAe;4BACjB,WAAW;4BACX,MAAM,cAAc,MAAM,iBAAW,CAAC,UAAU,CAAC,eAAe;gCAC9D,OAAO,OAAO,IAAI;gCAClB,UAAU,OAAO,QAAQ;4BAC3B;4BAEA,iBACE,cAAc,GAAG,CAAC,CAAC,OACjB,KAAK,EAAE,KAAK,gBAAgB,cAAc;wBAGhD,OAAO;4BACL,UAAU;4BACV,MAAM,UAAU,MAAM,iBAAW,CAAC,UAAU,CAAC;gCAC3C,OAAO,OAAO,IAAI;gCAClB,UAAU,OAAO,QAAQ;4BAC3B;4BAEA,iBAAiB;gCAAC;mCAAY;6BAAc;wBAC9C;wBAEA,SAAS;wBACT,IAAI;4BACF,MAAM,QAAQ,MAAM,iBAAW,CAAC,YAAY;4BAC5C,aAAa;wBACf,EAAE,OAAO,YAAY;wBACnB,kBAAkB;wBACpB;wBAEA,aAAa;wBACb,oBAAoB;wBACpB,iBAAiB;wBACjB,SAAS,WAAW;oBACtB,EAAE,OAAO,OAAO;oBACd,iBAAiB;oBACnB;gBACF;gBAEA,MAAM,mBAAmB,OAAO;oBAC9B,IAAI;wBACF,MAAM,iBAAW,CAAC,UAAU,CAAC;wBAC7B,iBAAiB,cAAc,MAAM,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;wBAE5D,SAAS;wBACT,IAAI;4BACF,MAAM,QAAQ,MAAM,iBAAW,CAAC,YAAY;4BAC5C,aAAa;wBACf,EAAE,OAAO,YAAY;wBACnB,kBAAkB;wBACpB;oBACF,EAAE,OAAO,OAAO;oBACd,iBAAiB;oBACnB;gBACF;gBAEA,qBACE,2BAAC,sBAAO;oBACN,OAAM;oBACN,OAAO;wBACL,cAAc;wBACd,QAAQ;wBACR,WAAW;oBACb;oBACA,WAAW;wBACT,cAAc;wBACd,eAAe;oBACjB;oBACA,WAAW;wBACT,SAAS;oBACX;;sCAGA,2BAAC;4BACC,OAAO;gCACL,cAAc;gCACd,SAAS;gCACT,YAAY;gCACZ,cAAc;gCACd,QAAQ;4BACV;;8CAGA,2BAAC;oCAAI,OAAO;wCACV,cAAc;wCACd,SAAS;wCACT,YAAY;wCACZ,cAAc;wCACd,QAAQ;wCACR,WAAW;oCACb;;sDAEE,2BAAC;4CAAI,OAAO;gDAAE,cAAc;4CAAG;sDAC7B,cAAA,2BAAC,UAAI;gDAAC,OAAM;gDAAS,KAAK;;kEACxB,2BAAC,oBAAa;wDAAC,OAAO;4DAAE,OAAO;4DAAW,UAAU;wDAAG;;;;;;kEACvD,2BAAC;wDAAK,MAAM;wDAAC,OAAO;4DAAE,UAAU;4DAAI,OAAO;wDAAU;kEAAG;;;;;;;;;;;;;;;;;sDAM5D,2BAAC,SAAG;4CAAC,QAAQ;gDAAC;gDAAI;6CAAG;;8DAEnB,2BAAC,SAAG;oDAAC,IAAI;oDAAI,IAAI;oDAAI,IAAI;oDAAI,IAAI;oDAAI,IAAI;8DACvC,cAAA,2BAAC,SAAG;wDAAC,QAAQ;4DAAC;4DAAI;yDAAG;;0EAEnB,2BAAC,SAAG;gEAAC,IAAI;gEAAI,IAAI;gEAAG,IAAI;gEAAG,IAAI;gEAAG,IAAI;0EACpC,cAAA,2BAAC;oEACC,OAAO;wEACL,YAAY;wEACZ,QAAQ;wEACR,cAAc;wEACd,SAAS;wEACT,WAAW;wEACX,UAAU;wEACV,UAAU;wEACV,WAAW;wEACX,YAAY;oEACd;;sFAGA,2BAAC;4EAAI,OAAO;gFACV,UAAU;gFACV,KAAK;gFACL,OAAO;gFACP,OAAO;gFACP,QAAQ;gFACR,YAAY;gFACZ,cAAc;4EAChB;;;;;;sFAEA,2BAAC,UAAI;4EAAC,QAAQ;4EAAC,OAAM;4EAAS,KAAK;;8FACjC,2BAAC;oFACC,OAAO;wFACL,OAAO;wFACP,QAAQ;wFACR,cAAc;wFACd,YAAY;wFACZ,WAAW;oFACb;;;;;;8FAEF,2BAAC;oFAAK,OAAO;wFAAE,UAAU;wFAAI,OAAO;wFAAW,YAAY;oFAAI;8FAAG;;;;;;8FAGlE,2BAAC;oFAAK,OAAO;wFACX,UAAU;wFACV,YAAY;wFACZ,OAAO;wFACP,YAAY;oFACd;8FACG,UAAU,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;0EAOpC,2BAAC,SAAG;gEAAC,IAAI;gEAAI,IAAI;gEAAG,IAAI;gEAAG,IAAI;gEAAG,IAAI;0EACpC,cAAA,2BAAC;oEACC,OAAO;wEACL,YAAY;wEACZ,QAAQ;wEACR,cAAc;wEACd,SAAS;wEACT,WAAW;wEACX,UAAU;wEACV,UAAU;wEACV,WAAW;wEACX,YAAY;oEACd;;sFAGA,2BAAC;4EAAI,OAAO;gFACV,UAAU;gFACV,KAAK;gFACL,OAAO;gFACP,OAAO;gFACP,QAAQ;gFACR,YAAY;gFACZ,cAAc;4EAChB;;;;;;sFAEA,2BAAC,UAAI;4EAAC,QAAQ;4EAAC,OAAM;4EAAS,KAAK;;8FACjC,2BAAC;oFACC,OAAO;wFACL,OAAO;wFACP,QAAQ;wFACR,cAAc;wFACd,YAAY;wFACZ,WAAW;oFACb;;;;;;8FAEF,2BAAC;oFAAK,OAAO;wFAAE,UAAU;wFAAI,OAAO;wFAAW,YAAY;oFAAI;8FAAG;;;;;;8FAGlE,2BAAC;oFAAK,OAAO;wFACX,UAAU;wFACV,YAAY;wFACZ,OAAO;wFACP,YAAY;oFACd;8FACG,UAAU,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;0EAOtC,2BAAC,SAAG;gEAAC,IAAI;gEAAI,IAAI;gEAAG,IAAI;gEAAG,IAAI;gEAAG,IAAI;0EACpC,cAAA,2BAAC;oEACC,OAAO;wEACL,YAAY;wEACZ,QAAQ;wEACR,cAAc;wEACd,SAAS;wEACT,WAAW;wEACX,UAAU;wEACV,UAAU;wEACV,WAAW;wEACX,YAAY;oEACd;;sFAGA,2BAAC;4EAAI,OAAO;gFACV,UAAU;gFACV,KAAK;gFACL,OAAO;gFACP,OAAO;gFACP,QAAQ;gFACR,YAAY;gFACZ,cAAc;4EAChB;;;;;;sFAEA,2BAAC,UAAI;4EAAC,QAAQ;4EAAC,OAAM;4EAAS,KAAK;;8FACjC,2BAAC;oFACC,OAAO;wFACL,OAAO;wFACP,QAAQ;wFACR,cAAc;wFACd,YAAY;wFACZ,WAAW;oFACb;;;;;;8FAEF,2BAAC;oFAAK,OAAO;wFAAE,UAAU;wFAAI,OAAO;wFAAW,YAAY;oFAAI;8FAAG;;;;;;8FAGlE,2BAAC;oFAAK,OAAO;wFACX,UAAU;wFACV,YAAY;wFACZ,OAAO;wFACP,YAAY;oFACd;8FACG,UAAU,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8DASvC,2BAAC,SAAG;oDAAC,IAAI;oDAAI,IAAI;oDAAI,IAAI;oDAAG,IAAI;oDAAG,IAAI;8DACrC,cAAA,2BAAC;wDACC,OAAO;4DACL,YAAY;4DACZ,QAAQ;4DACR,cAAc;4DACd,SAAS;4DACT,QAAQ;4DACR,SAAS;4DACT,eAAe;4DACf,YAAY;4DACZ,gBAAgB;4DAChB,UAAU;4DACV,UAAU;4DACV,WAAW;4DACX,WAAW;wDACb;;0EAGA,2BAAC;gEAAI,OAAO;oEACV,UAAU;oEACV,KAAK;oEACL,OAAO;oEACP,OAAO;oEACP,QAAQ;oEACR,YAAY;oEACZ,cAAc;gEAChB;;;;;;0EAEA,2BAAC,aAAO;gEACN,OAAO,CAAC,MAAM,EAAE,UAAU,cAAc,CAAC,CAAC,EAAE,UAAU,UAAU,CAAC,OAAO,CAAC;0EAEzE,cAAA,2BAAC,UAAI;oEAAC,QAAQ;oEAAC,OAAM;oEAAS,KAAK;;sFACjC,2BAAC;4EAAK,OAAO;gFAAE,UAAU;gFAAI,OAAO;gFAAW,YAAY;4EAAI;sFAAG;;;;;;sFAKlE,2BAAC,cAAQ;4EACP,MAAK;4EACL,SAAS,UAAU,oBAAoB;4EACvC,MAAM;4EACN,aAAa;gFACX,MAAM;gFACN,QAAQ;4EACV;4EACA,YAAW;4EACX,aAAa;4EACb,QAAQ,CAAC,wBACP,2BAAC;oFAAK,OAAO;wFACX,UAAU;wFACV,YAAY;wFACZ,OAAO;oFACT;;wFACG;wFAAQ;;;;;;;;;;;;sFAKf,2BAAC;4EAAK,OAAO;gFAAE,UAAU;gFAAI,OAAO;gFAAW,WAAW;4EAAS;;gFAChE,UAAU,cAAc;gFAAC;gFAAE,UAAU,UAAU;gFAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAU/D,2BAAC;oCAAI,OAAO;wCACV,cAAc;wCACd,SAAS;wCACT,YAAY;wCACZ,cAAc;wCACd,QAAQ;wCACR,WAAW;oCACb;8CACE,cAAA,2BAAC,SAAG;wCAAC,QAAQ;4CAAC;4CAAI;yCAAG;wCAAE,OAAM;;0DAC3B,2BAAC,SAAG;gDAAC,IAAI;gDAAI,IAAI;gDAAI,IAAI;gDAAI,IAAI;gDAAI,IAAI;0DACvC,cAAA,2BAAC;oDAAI,OAAO;wDAAE,UAAU;oDAAW;;sEACjC,2BAAC,WAAK,CAAC,MAAM;4DACX,aAAY;4DACZ,UAAU;4DACV,sBAAQ,2BAAC,qBAAc;gEAAC,OAAO;oEAAE,OAAO;gEAAU;;;;;;4DAClD,OAAO;4DACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4DAC7C,OAAO;gEACL,OAAO;gEACP,cAAc;gEACd,WAAW;4DACb;4DACA,MAAK;;;;;;wDAGN,WAAW,IAAI,oBACd,2BAAC;4DAAI,OAAO;gEACV,UAAU;gEACV,KAAK;gEACL,MAAM;gEACN,OAAO;gEACP,WAAW;gEACX,SAAS;gEACT,YAAY;gEACZ,QAAQ;gEACR,cAAc;gEACd,UAAU;gEACV,OAAO;gEACP,WAAW;gEACX,QAAQ;4DACV;sEACE,cAAA,2BAAC,UAAI;gEAAC,OAAM;gEAAS,KAAK;;kFACxB,2BAAC,qBAAc;wEAAC,OAAO;4EAAE,UAAU;wEAAG;;;;;;kFACtC,2BAAC;wEAAK,OAAO;4EAAE,UAAU;wEAAG;;4EAAG;4EACrB;4EAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAQ/B,2BAAC,SAAG;gDAAC,IAAI;gDAAI,IAAI;gDAAI,IAAI;gDAAG,IAAI;gDAAG,IAAI;0DACrC,cAAA,2BAAC,YAAM;oDACL,MAAK;oDACL,oBAAM,2BAAC,mBAAY;;;;;oDACnB,SAAS;wDACP,iBAAiB;wDACjB,SAAS,WAAW;wDACpB,oBAAoB;oDACtB;oDACA,MAAK;oDACL,OAAO;wDACL,OAAO;wDACP,cAAc;wDACd,YAAY;wDACZ,QAAQ;wDACR,WAAW;wDACX,YAAY;oDACd;8DACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAST,2BAAC;4BAAI,OAAO;gCAAE,cAAc;4BAAG;sCAC7B,cAAA,2BAAC,UAAI;gCACH,WAAW;gCACX,UAAU,CAAC,MAAQ,aAAa;gCAChC,MAAK;gCACL,OAAO;oCACL,YAAY;oCACZ,cAAc;oCACd,SAAS;oCACT,WAAW;gCACb;gCACA,aAAa;oCACX,cAAc;oCACd,cAAc;gCAChB;;kDAEA,2BAAC;wCACC,mBACE,2BAAC,UAAI;4CAAC,OAAM;4CAAS,KAAK;;8DACxB,2BAAC;;;;;8DACD,2BAAC;8DAAK;;;;;;;;;;;;uCAGN;;;;;kDAEN,2BAAC;wCACC,mBACE,2BAAC,UAAI;4CAAC,OAAM;4CAAS,KAAK;;8DACxB,2BAAC;;;;;8DACD,2BAAC;8DAAK;;;;;;;;;;;;uCAGN;;;;;kDAEN,2BAAC;wCACC,mBACE,2BAAC,UAAI;4CAAC,OAAM;4CAAS,KAAK;;8DACxB,2BAAC;;;;;8DACD,2BAAC;8DAAK;;;;;;;;;;;;uCAGN;;;;;;;;;;;;;;;;wBAMT,cAAc,MAAM,GAAG,KAAK,CAAC,WAAW,CAAC,uBACxC,2BAAC;4BAAI,OAAO;gCACV,cAAc;gCACd,SAAS;gCACT,YAAY,WAAW,IAAI,KACtB,sBAAsB,MAAM,GAAG,IAC5B,sDACA,sDACJ;gCACJ,cAAc;gCACd,QAAQ,WAAW,IAAI,KAClB,sBAAsB,MAAM,GAAG,IAC5B,sBACA,sBACJ;gCACJ,WAAW;4BACb;sCACE,cAAA,2BAAC,UAAI;gCAAC,OAAM;gCAAS,KAAK;0CACvB,WAAW,IAAI,KACd,sBAAsB,MAAM,GAAG,kBAC7B;;sDACE,2BAAC;4CAAoB,OAAO;gDAAE,OAAO;gDAAW,UAAU;4CAAG;;;;;;sDAC7D,2BAAC;4CAAK,OAAO;gDAAE,UAAU;gDAAI,OAAO;4CAAU;;gDAAG;8DAC5C,2BAAC;oDAAK,MAAM;oDAAC,OAAO;wDAAE,OAAO;wDAAW,UAAU;oDAAG;8DAAI,sBAAsB,MAAM;;;;;;gDAAQ;;;;;;;;iEAIpG;;sDACE,2BAAC;4CAA0B,OAAO;gDAAE,OAAO;gDAAW,UAAU;4CAAG;;;;;;sDACnE,2BAAC;4CAAK,OAAO;gDAAE,UAAU;gDAAI,OAAO;4CAAU;sDAAG;;;;;;;iEAMrD;;sDACE,2BAAC;4CAAsB,OAAO;gDAAE,OAAO;gDAAW,UAAU;4CAAG;;;;;;sDAC/D,2BAAC;4CAAK,OAAO;gDAAE,UAAU;gDAAI,OAAO;4CAAU;;gDAAG;8DAC5C,2BAAC;oDAAK,MAAM;oDAAC,OAAO;wDAAE,OAAO;wDAAW,UAAU;oDAAG;8DAAI,sBAAsB,MAAM;;;;;;gDAAQ;;;;;;;;;;;;;;;;;;;wBAS3G,sBACC,2BAAC,WAAK;4BACJ,SAAQ;4BACR,aAAa;4BACb,MAAK;4BACL,QAAQ;4BACR,OAAO;gCAAE,cAAc;4BAAG;;;;;iDAG5B,2BAAC,UAAI;4BAAC,UAAU;;8CACd,2BAAC,sBAAO;oCACN,YAAY;oCACZ,YAAY;wCACV,SAAS,WAAW,OAAO;wCAC3B,UAAU,WAAW,QAAQ;wCAC7B,OAAO,sBAAsB,MAAM;wCACnC,iBAAiB,WAAW,eAAe;wCAC3C,iBAAiB,WAAW,eAAe;wCAC3C,WAAW,WAAW,SAAS;wCAC/B,iBAAiB,WAAW,eAAe;wCAC3C,UAAU,WAAW,QAAQ;wCAC7B,kBAAkB,WAAW,gBAAgB;oCAC/C;oCACA,YAAY,CAAC;wCACX,qBACE,2BAAC;4CACC,WAAU;4CACV,OAAO;gDACL,SAAS;gDACT,cAAc;gDACd,cAAc;gDACd,YAAY;gDACZ,SAAS,KAAK,MAAM,KAAK,IAAI,MAAM;gDACnC,YAAY,CAAC,UAAU,EACrB,KAAK,MAAM,KAAK,IACZ,YACA,KAAK,QAAQ,KAAK,IAChB,YACA,KAAK,QAAQ,KAAK,IAChB,YACA,UACT,CAAC;gDACF,WAAW;4CACb;sDAEA,cAAA,2BAAC,UAAI;gDAAC,OAAM;gDAAS,KAAK;gDAAI,OAAO;oDAAE,OAAO;gDAAO;;kEAEnD,2BAAC,UAAI;wDAAC,QAAQ;wDAAC,OAAM;;4DAClB,KAAK,MAAM,KAAK,kBACf,2BAAC,UAAI;gEACH,OAAM;gEACN,SAAQ;gEACR,OAAO;oEACL,OAAO;oEACP,QAAQ;oEACR,cAAc;oEACd,YAAY;gEACd;0EAEA,cAAA,2BAAC,oBAAa;oEACZ,OAAO;wEAAE,OAAO;wEAAQ,UAAU;oEAAG;;;;;;;;;;uFAIzC,2BAAC;gEACC,OAAO;oEACL,OAAO;oEACP,QAAQ;oEACR,cAAc;oEACd,QAAQ,CAAC,UAAU,EACjB,KAAK,QAAQ,KAAK,IACd,YACA,KAAK,QAAQ,KAAK,IAChB,YACA,UACP,CAAC;gEACJ;;;;;;0EAIJ,2BAAC;gEACC,OAAO;oEACL,OAAO;oEACP,QAAQ;oEACR,YAAY;oEACZ,WAAW;gEACb;;;;;;;;;;;;kEAKJ,2BAAC,UAAI;wDAAC,QAAQ;wDAAC,OAAO;4DAAE,MAAM;wDAAE;;0EAC9B,2BAAC;gEACC,OAAO;oEACL,UAAU;oEACV,YAAY,KAAK,QAAQ,KAAK,IAAI,MAAM;oEACxC,gBACE,KAAK,MAAM,KAAK,IAAI,iBAAiB;oEACvC,OAAO,KAAK,MAAM,KAAK,IAAI,YAAY;gEACzC;0EAEC,KAAK,KAAK;;;;;;0EAIb,2BAAC,WAAK;gEAAC,OAAM;gEAAS,MAAM;gEAAG,OAAO;oEAAE,WAAW;gEAAE;;kFACnD,2BAAC,uBAAgB;wEACf,OAAO;4EACL,UAAU;4EACV,OAAO;wEACT;;;;;;kFAEF,2BAAC;wEAAK,MAAK;wEAAY,OAAO;4EAAE,UAAU;wEAAG;;4EAAG;4EACzC;4EACJ,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;kEAMnD,2BAAC,cAAQ;wDACP,SAAS;4DAAC;yDAAQ;wDAClB,MAAM;4DACJ,OAAO;gEACL;oEACE,KAAK;oEACL,OACE,KAAK,MAAM,KAAK,IAAI,UAAU;oEAChC,oBACE,2BAAC,oBAAa;wEACZ,OAAO;4EACL,OACE,KAAK,MAAM,KAAK,IAAI,YAAY;4EAClC,UAAU;wEACZ;;;;;;gEAGN;gEACA;oEACE,KAAK;oEACL,OAAO;oEACP,oBAAM,2BAAC,mBAAY;wEAAC,OAAO;4EAAE,OAAO;wEAAU;;;;;;gEAChD;gEACA;oEACE,KAAK;oEACL,OAAO;oEACP,oBACE,2BAAC,qBAAc;wEAAC,OAAO;4EAAE,OAAO;wEAAU;;;;;;oEAE5C,QAAQ;gEACV;6DACD;4DACD,SAAS,CAAC,EAAE,GAAG,EAAE;gEACf,IAAI,QAAQ,YACV,uBAAuB,KAAK,EAAE;qEACzB,IAAI,QAAQ,QAAQ;oEACzB,iBAAiB,KAAK,EAAE;oEACxB,SAAS,cAAc,CAAC;wEACtB,MAAM,KAAK,KAAK;wEAChB,UAAU,KAAK,QAAQ;oEACzB;oEACA,oBAAoB;gEACtB,OAAO,IAAI,QAAQ,UACjB,iBAAiB,KAAK,EAAE;4DAE5B;wDACF;kEAEA,cAAA,2BAAC,YAAM;4DACL,MAAK;4DACL,MAAK;4DACL,oBAAM,2BAAC,mBAAY;;;;;4DACnB,OAAO;gEAAE,OAAO;gEAAI,QAAQ;4DAAG;;;;;;;;;;;;;;;;;;;;;;oCAM3C;;;;;;8CAIF,2BAAC,wBAAS;oCACR,OAAO,gBAAgB,WAAW;oCAClC,MAAM;oCACN,cAAc,CAAC;wCACb,oBAAoB;wCACpB,IAAI,CAAC,SAAS;4CACZ,iBAAiB;4CACjB,SAAS,WAAW;wCACtB;oCACF;oCACA,MAAM;oCACN,QAAO;oCACP,UAAU;oCACV,cAAa;oCACb,OAAO;oCACP,YAAY;wCACV,UAAU;wCACV,gBAAgB;wCAChB,cAAc;wCACd,UAAU;wCACV,aAAa;oCACf;oCACA,WAAW;wCACT,cAAc;4CACZ,YAAY,gBAAgB,SAAS;4CACrC,WAAW;wCACb;wCACA,mBAAmB;4CACjB,OAAO;gDACL,YAAY;gDACZ,aAAa;gDACb,WAAW;4CACb;4CACA,MAAM,8BAAgB,2BAAC,mBAAY;;;;uEAAM,2BAAC,mBAAY;;;;;wCACxD;wCACA,kBAAkB;4CAChB,OAAO;gDACL,aAAa;4CACf;wCACF;wCACA,SAAS;4CACP,oBAAoB;4CACpB,iBAAiB;4CACjB,SAAS,WAAW;wCACtB;oCACF;oCACA,UAAU;;sDAEV,2BAAC,UAAI,CAAC,IAAI;4CACR,MAAK;4CACL,OAAM;4CACN,OAAO;gDAAC;oDAAE,UAAU;oDAAM,SAAS;gDAAU;6CAAE;sDAE/C,cAAA,2BAAC,WAAK;gDACJ,aAAY;gDACZ,MAAK;gDACL,OAAO;oDAAE,cAAc;gDAAE;;;;;;;;;;;sDAI7B,2BAAC,UAAI,CAAC,IAAI;4CACR,MAAK;4CACL,OAAM;4CACN,cAAc;4CACd,OAAO;gDAAC;oDAAE,UAAU;oDAAM,SAAS;gDAAS;6CAAE;sDAE9C,cAAA,2BAAC,YAAM;gDACL,MAAK;gDACL,SAAS;oDACP;wDAAE,OAAO;wDAAG,OAAO;oDAAO;oDAC1B;wDAAE,OAAO;wDAAG,OAAO;oDAAO;oDAC1B;wDAAE,OAAO;wDAAG,OAAO;oDAAO;iDAC3B;gDACD,OAAO;oDAAE,cAAc;gDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQzC;eAr4BM;;oBAgBe,UAAI,CAAC;oBAUY,8BAAa;;;iBA1B7C;gBAu4BN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;ID96BD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,gCAA+B;YAAC;SAAqB;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AACrjB"}