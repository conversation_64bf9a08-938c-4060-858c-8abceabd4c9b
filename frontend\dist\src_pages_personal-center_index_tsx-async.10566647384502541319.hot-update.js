globalThis.makoModuleHotUpdate('src/pages/personal-center/index.tsx', {
    modules: {
        "src/pages/personal-center/TodoManagement.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _todo = __mako_require__("src/services/todo.ts");
            var _paginationUtils = __mako_require__("src/utils/paginationUtils.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Text } = _antd.Typography;
            const { TabPane } = _antd.Tabs;
            const TodoManagement = ()=>{
                _s();
                // TODO数据状态管理
                const [personalTasks, setPersonalTasks] = (0, _react.useState)([]);
                const [todoStats, setTodoStats] = (0, _react.useState)({
                    highPriorityCount: 0,
                    mediumPriorityCount: 0,
                    lowPriorityCount: 0,
                    totalCount: 0,
                    completedCount: 0,
                    completionPercentage: 0
                });
                const [loading, setLoading] = (0, _react.useState)(true);
                const [error, setError] = (0, _react.useState)(null);
                // 待办事项状态管理
                const [todoModalVisible, setTodoModalVisible] = (0, _react.useState)(false);
                const [todoForm] = _antd.Form.useForm();
                const [editingTodoId, setEditingTodoId] = (0, _react.useState)(null);
                // 过滤器状态
                const [activeTab, setActiveTab] = (0, _react.useState)('pending');
                const [searchText, setSearchText] = (0, _react.useState)('');
                // 分页功能
                const { pagination, updateTotal } = (0, _paginationUtils.usePagination)({
                    defaultPageSize: 10,
                    pageSizeOptions: [
                        '5',
                        '10',
                        '20',
                        '50'
                    ],
                    showTotal: (total, range)=>`共 ${total} 条待办事项，显示第 ${range[0]}-${range[1]} 条`
                });
                // 获取TODO数据
                (0, _react.useEffect)(()=>{
                    const fetchTodoData = async ()=>{
                        try {
                            setLoading(true);
                            setError(null);
                            console.log('TodoManagement: 开始获取TODO数据');
                            // 分别获取TODO列表和统计数据，避免一个失败影响另一个
                            const todosPromise = _todo.TodoService.getUserTodos().catch((error)=>{
                                console.error('获取TODO列表失败:', error);
                                return [];
                            });
                            const statsPromise = _todo.TodoService.getTodoStats().catch((error)=>{
                                console.error('获取TODO统计失败:', error);
                                return {
                                    highPriorityCount: 0,
                                    mediumPriorityCount: 0,
                                    lowPriorityCount: 0,
                                    totalCount: 0,
                                    completedCount: 0,
                                    completionPercentage: 0
                                };
                            });
                            const [todos, stats] = await Promise.all([
                                todosPromise,
                                statsPromise
                            ]);
                            console.log('TodoManagement: 获取到TODO列表:', todos);
                            console.log('TodoManagement: 获取到统计数据:', stats);
                            setPersonalTasks(todos);
                            setTodoStats(stats);
                        } catch (error) {
                            console.error('获取TODO数据时发生未知错误:', error);
                            setError('获取TODO数据失败，请刷新页面重试');
                        } finally{
                            setLoading(false);
                        }
                    };
                    fetchTodoData();
                }, []);
                // 根据激活的标签和搜索文本过滤任务
                const filteredPersonalTasks = (0, _react.useMemo)(()=>{
                    return (personalTasks || []).filter((task)=>{
                        // 根据标签过滤
                        if (activeTab === 'pending' && task.status === 1) return false;
                        if (activeTab === 'completed' && task.status === 0) return false;
                        // 根据搜索文本过滤
                        if (searchText && !task.title.toLowerCase().includes(searchText.toLowerCase())) return false;
                        return true;
                    });
                }, [
                    personalTasks,
                    activeTab,
                    searchText
                ]);
                // 更新总数
                _react.default.useEffect(()=>{
                    updateTotal(filteredPersonalTasks.length);
                }, [
                    filteredPersonalTasks.length,
                    updateTotal
                ]);
                // 处理待办事项操作
                const handleToggleTodoStatus = async (id)=>{
                    try {
                        const task = personalTasks.find((t)=>t.id === id);
                        if (!task) return;
                        const newStatus = task.status === 0 ? 1 : 0;
                        await _todo.TodoService.updateTodo(id, {
                            status: newStatus
                        });
                        // 更新本地状态
                        setPersonalTasks(personalTasks.map((task)=>task.id === id ? {
                                ...task,
                                status: newStatus
                            } : task));
                        // 刷新统计数据
                        try {
                            const stats = await _todo.TodoService.getTodoStats();
                            setTodoStats(stats);
                        } catch (statsError) {
                        // 统计数据刷新失败不影响主要操作
                        }
                    } catch (error) {
                    // 错误处理由响应拦截器统一处理
                    }
                };
                const handleAddOrUpdateTodo = async (values)=>{
                    try {
                        if (editingTodoId) {
                            // 更新现有待办事项
                            const updatedTodo = await _todo.TodoService.updateTodo(editingTodoId, {
                                title: values.name,
                                priority: values.priority
                            });
                            setPersonalTasks(personalTasks.map((task)=>task.id === editingTodoId ? updatedTodo : task));
                        } else {
                            // 添加新待办事项
                            const newTodo = await _todo.TodoService.createTodo({
                                title: values.name,
                                priority: values.priority
                            });
                            setPersonalTasks([
                                newTodo,
                                ...personalTasks
                            ]);
                        }
                        // 刷新统计数据
                        try {
                            const stats = await _todo.TodoService.getTodoStats();
                            setTodoStats(stats);
                        } catch (statsError) {
                        // 统计数据刷新失败不影响主要操作
                        }
                        // 重置表单并关闭模态框
                        setTodoModalVisible(false);
                        setEditingTodoId(null);
                        todoForm.resetFields();
                    } catch (error) {
                    // 错误处理由响应拦截器统一处理
                    }
                };
                const handleDeleteTodo = async (id)=>{
                    try {
                        await _todo.TodoService.deleteTodo(id);
                        setPersonalTasks(personalTasks.filter((task)=>task.id !== id));
                        // 刷新统计数据
                        try {
                            const stats = await _todo.TodoService.getTodoStats();
                            setTodoStats(stats);
                        } catch (statsError) {
                        // 统计数据刷新失败不影响主要操作
                        }
                    } catch (error) {
                    // 错误处理由响应拦截器统一处理
                    }
                };
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProCard, {
                    title: "待办事项/任务列表",
                    style: {
                        borderRadius: 8,
                        height: 'fit-content',
                        minHeight: '600px'
                    },
                    headStyle: {
                        borderBottom: '1px solid #f0f0f0',
                        paddingBottom: 12
                    },
                    bodyStyle: {
                        padding: '16px'
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                marginBottom: 16,
                                padding: '12px 16px',
                                background: '#fafbfc',
                                borderRadius: 8,
                                border: '1px solid #f0f0f0'
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        marginBottom: 20,
                                        padding: '20px',
                                        background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',
                                        borderRadius: 16,
                                        border: '1px solid #e2e8f0',
                                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                marginBottom: 20
                                            },
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                align: "center",
                                                gap: 10,
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckOutlined, {
                                                        style: {
                                                            color: '#1890ff',
                                                            fontSize: 18
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 275,
                                                        columnNumber: 15
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                        strong: true,
                                                        style: {
                                                            fontSize: 18,
                                                            color: '#1f2937'
                                                        },
                                                        children: "任务统计概览"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 276,
                                                        columnNumber: 15
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                lineNumber: 274,
                                                columnNumber: 13
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 273,
                                            columnNumber: 11
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                            gutter: [
                                                20,
                                                16
                                            ],
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                    xs: 24,
                                                    sm: 24,
                                                    md: 16,
                                                    lg: 16,
                                                    xl: 18,
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                                        gutter: [
                                                            12,
                                                            12
                                                        ],
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                                xs: 24,
                                                                sm: 8,
                                                                md: 8,
                                                                lg: 8,
                                                                xl: 8,
                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        background: 'linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%)',
                                                                        border: '2px solid #fca5a5',
                                                                        borderRadius: 12,
                                                                        padding: '16px',
                                                                        textAlign: 'center',
                                                                        position: 'relative',
                                                                        overflow: 'hidden',
                                                                        boxShadow: '0 2px 4px rgba(239, 68, 68, 0.1)',
                                                                        transition: 'all 0.3s ease'
                                                                    },
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                            style: {
                                                                                position: 'absolute',
                                                                                top: -20,
                                                                                right: -20,
                                                                                width: 40,
                                                                                height: 40,
                                                                                background: 'rgba(239, 68, 68, 0.1)',
                                                                                borderRadius: '50%'
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                            lineNumber: 302,
                                                                            columnNumber: 21
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                            vertical: true,
                                                                            align: "center",
                                                                            gap: 8,
                                                                            children: [
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                    style: {
                                                                                        width: 12,
                                                                                        height: 12,
                                                                                        borderRadius: '50%',
                                                                                        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
                                                                                        boxShadow: '0 2px 4px rgba(239, 68, 68, 0.3)'
                                                                                    }
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                                    lineNumber: 313,
                                                                                    columnNumber: 23
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                    style: {
                                                                                        fontSize: 12,
                                                                                        color: '#6b7280',
                                                                                        fontWeight: 500
                                                                                    },
                                                                                    children: "高优先级"
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                                    lineNumber: 322,
                                                                                    columnNumber: 23
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                    style: {
                                                                                        fontSize: 24,
                                                                                        fontWeight: 700,
                                                                                        color: '#dc2626',
                                                                                        lineHeight: 1
                                                                                    },
                                                                                    children: todoStats.highPriorityCount
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                                    lineNumber: 325,
                                                                                    columnNumber: 23
                                                                                }, this)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                            lineNumber: 312,
                                                                            columnNumber: 21
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                    lineNumber: 288,
                                                                    columnNumber: 19
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                lineNumber: 287,
                                                                columnNumber: 17
                                                            }, this),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                                xs: 24,
                                                                sm: 8,
                                                                md: 8,
                                                                lg: 8,
                                                                xl: 8,
                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        background: 'linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%)',
                                                                        border: '2px solid #fcd34d',
                                                                        borderRadius: 12,
                                                                        padding: '16px',
                                                                        textAlign: 'center',
                                                                        position: 'relative',
                                                                        overflow: 'hidden',
                                                                        boxShadow: '0 2px 4px rgba(245, 158, 11, 0.1)',
                                                                        transition: 'all 0.3s ease'
                                                                    },
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                            style: {
                                                                                position: 'absolute',
                                                                                top: -20,
                                                                                right: -20,
                                                                                width: 40,
                                                                                height: 40,
                                                                                background: 'rgba(245, 158, 11, 0.1)',
                                                                                borderRadius: '50%'
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                            lineNumber: 353,
                                                                            columnNumber: 21
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                            vertical: true,
                                                                            align: "center",
                                                                            gap: 8,
                                                                            children: [
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                    style: {
                                                                                        width: 12,
                                                                                        height: 12,
                                                                                        borderRadius: '50%',
                                                                                        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
                                                                                        boxShadow: '0 2px 4px rgba(245, 158, 11, 0.3)'
                                                                                    }
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                                    lineNumber: 364,
                                                                                    columnNumber: 23
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                    style: {
                                                                                        fontSize: 12,
                                                                                        color: '#6b7280',
                                                                                        fontWeight: 500
                                                                                    },
                                                                                    children: "中优先级"
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                                    lineNumber: 373,
                                                                                    columnNumber: 23
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                    style: {
                                                                                        fontSize: 24,
                                                                                        fontWeight: 700,
                                                                                        color: '#d97706',
                                                                                        lineHeight: 1
                                                                                    },
                                                                                    children: todoStats.mediumPriorityCount
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                                    lineNumber: 376,
                                                                                    columnNumber: 23
                                                                                }, this)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                            lineNumber: 363,
                                                                            columnNumber: 21
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                    lineNumber: 339,
                                                                    columnNumber: 19
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                lineNumber: 338,
                                                                columnNumber: 17
                                                            }, this),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                                xs: 24,
                                                                sm: 8,
                                                                md: 8,
                                                                lg: 8,
                                                                xl: 8,
                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        background: 'linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%)',
                                                                        border: '2px solid #d1d5db',
                                                                        borderRadius: 12,
                                                                        padding: '16px',
                                                                        textAlign: 'center',
                                                                        position: 'relative',
                                                                        overflow: 'hidden',
                                                                        boxShadow: '0 2px 4px rgba(107, 114, 128, 0.1)',
                                                                        transition: 'all 0.3s ease'
                                                                    },
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                            style: {
                                                                                position: 'absolute',
                                                                                top: -20,
                                                                                right: -20,
                                                                                width: 40,
                                                                                height: 40,
                                                                                background: 'rgba(107, 114, 128, 0.1)',
                                                                                borderRadius: '50%'
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                            lineNumber: 404,
                                                                            columnNumber: 21
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                            vertical: true,
                                                                            align: "center",
                                                                            gap: 8,
                                                                            children: [
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                    style: {
                                                                                        width: 12,
                                                                                        height: 12,
                                                                                        borderRadius: '50%',
                                                                                        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',
                                                                                        boxShadow: '0 2px 4px rgba(107, 114, 128, 0.3)'
                                                                                    }
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                                    lineNumber: 415,
                                                                                    columnNumber: 23
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                    style: {
                                                                                        fontSize: 12,
                                                                                        color: '#6b7280',
                                                                                        fontWeight: 500
                                                                                    },
                                                                                    children: "低优先级"
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                                    lineNumber: 424,
                                                                                    columnNumber: 23
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                    style: {
                                                                                        fontSize: 24,
                                                                                        fontWeight: 700,
                                                                                        color: '#4b5563',
                                                                                        lineHeight: 1
                                                                                    },
                                                                                    children: todoStats.lowPriorityCount
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                                    lineNumber: 427,
                                                                                    columnNumber: 23
                                                                                }, this)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                            lineNumber: 414,
                                                                            columnNumber: 21
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                    lineNumber: 390,
                                                                    columnNumber: 19
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                lineNumber: 389,
                                                                columnNumber: 17
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 285,
                                                        columnNumber: 15
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 284,
                                                    columnNumber: 13
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                    xs: 24,
                                                    sm: 24,
                                                    md: 8,
                                                    lg: 8,
                                                    xl: 6,
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            background: 'linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%)',
                                                            border: '2px solid #86efac',
                                                            borderRadius: 12,
                                                            padding: '20px',
                                                            height: '100%',
                                                            display: 'flex',
                                                            flexDirection: 'column',
                                                            alignItems: 'center',
                                                            justifyContent: 'center',
                                                            position: 'relative',
                                                            overflow: 'hidden',
                                                            boxShadow: '0 4px 6px rgba(34, 197, 94, 0.1)',
                                                            minHeight: '140px'
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                style: {
                                                                    position: 'absolute',
                                                                    top: -30,
                                                                    right: -30,
                                                                    width: 60,
                                                                    height: 60,
                                                                    background: 'rgba(34, 197, 94, 0.1)',
                                                                    borderRadius: '50%'
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                lineNumber: 461,
                                                                columnNumber: 17
                                                            }, this),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                                                title: `完成情况: ${todoStats.completedCount}/${todoStats.totalCount} 个任务已完成`,
                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                    vertical: true,
                                                                    align: "center",
                                                                    gap: 12,
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                            style: {
                                                                                fontSize: 14,
                                                                                color: '#6b7280',
                                                                                fontWeight: 500
                                                                            },
                                                                            children: "任务完成率"
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                            lineNumber: 475,
                                                                            columnNumber: 21
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Progress, {
                                                                            type: "circle",
                                                                            percent: todoStats.completionPercentage,
                                                                            size: 80,
                                                                            strokeColor: {
                                                                                '0%': '#10b981',
                                                                                '100%': '#059669'
                                                                            },
                                                                            trailColor: "#e5e7eb",
                                                                            strokeWidth: 8,
                                                                            format: (percent)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                                    style: {
                                                                                        fontSize: 16,
                                                                                        fontWeight: 700,
                                                                                        color: '#059669'
                                                                                    },
                                                                                    children: [
                                                                                        percent,
                                                                                        "%"
                                                                                    ]
                                                                                }, void 0, true, {
                                                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                                    lineNumber: 491,
                                                                                    columnNumber: 25
                                                                                }, void 0)
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                            lineNumber: 480,
                                                                            columnNumber: 21
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                            style: {
                                                                                fontSize: 12,
                                                                                color: '#6b7280',
                                                                                textAlign: 'center'
                                                                            },
                                                                            children: [
                                                                                todoStats.completedCount,
                                                                                "/",
                                                                                todoStats.totalCount,
                                                                                " 已完成"
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                            lineNumber: 501,
                                                                            columnNumber: 21
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                    lineNumber: 474,
                                                                    columnNumber: 19
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                lineNumber: 471,
                                                                columnNumber: 17
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 443,
                                                        columnNumber: 15
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 442,
                                                    columnNumber: 13
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 282,
                                            columnNumber: 11
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 264,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        marginBottom: 20,
                                        padding: '16px',
                                        background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',
                                        borderRadius: 12,
                                        border: '1px solid #e2e8f0',
                                        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)'
                                    },
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                        gutter: [
                                            16,
                                            12
                                        ],
                                        align: "middle",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                xs: 24,
                                                sm: 24,
                                                md: 16,
                                                lg: 18,
                                                xl: 20,
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    style: {
                                                        position: 'relative'
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input.Search, {
                                                            placeholder: "搜索任务标题、描述或标签...",
                                                            allowClear: true,
                                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {
                                                                style: {
                                                                    color: '#6b7280'
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                lineNumber: 526,
                                                                columnNumber: 27
                                                            }, void 0),
                                                            value: searchText,
                                                            onChange: (e)=>setSearchText(e.target.value),
                                                            style: {
                                                                width: '100%',
                                                                borderRadius: 8,
                                                                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
                                                            },
                                                            size: "large"
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                            lineNumber: 523,
                                                            columnNumber: 17
                                                        }, this),
                                                        searchText.trim() && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                            style: {
                                                                position: 'absolute',
                                                                top: '100%',
                                                                left: 0,
                                                                right: 0,
                                                                marginTop: 4,
                                                                padding: '6px 12px',
                                                                background: '#ffffff',
                                                                border: '1px solid #e2e8f0',
                                                                borderRadius: 6,
                                                                fontSize: 12,
                                                                color: '#6b7280',
                                                                boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                                                                zIndex: 10
                                                            },
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                align: "center",
                                                                gap: 6,
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {
                                                                        style: {
                                                                            fontSize: 12
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                        lineNumber: 554,
                                                                        columnNumber: 23
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        style: {
                                                                            fontSize: 12
                                                                        },
                                                                        children: [
                                                                            '正在搜索: "',
                                                                            searchText,
                                                                            '"'
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                        lineNumber: 555,
                                                                        columnNumber: 23
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                lineNumber: 553,
                                                                columnNumber: 21
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                            lineNumber: 538,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 522,
                                                    columnNumber: 15
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                lineNumber: 521,
                                                columnNumber: 13
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                xs: 24,
                                                sm: 24,
                                                md: 8,
                                                lg: 6,
                                                xl: 4,
                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                    type: "primary",
                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PlusOutlined, {}, void 0, false, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 567,
                                                        columnNumber: 23
                                                    }, void 0),
                                                    onClick: ()=>{
                                                        setEditingTodoId(null);
                                                        todoForm.resetFields();
                                                        setTodoModalVisible(true);
                                                    },
                                                    size: "large",
                                                    style: {
                                                        width: '100%',
                                                        borderRadius: 8,
                                                        background: 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
                                                        border: 'none',
                                                        boxShadow: '0 2px 4px rgba(24, 144, 255, 0.3)',
                                                        fontWeight: 500
                                                    },
                                                    children: "新建任务"
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 565,
                                                    columnNumber: 15
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                lineNumber: 564,
                                                columnNumber: 13
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                        lineNumber: 520,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 512,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                            lineNumber: 254,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                marginBottom: 16
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tabs, {
                                activeKey: activeTab,
                                onChange: (key)=>setActiveTab(key),
                                size: "large",
                                style: {
                                    background: '#ffffff',
                                    borderRadius: 8,
                                    padding: '0 16px',
                                    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
                                },
                                tabBarStyle: {
                                    marginBottom: 0,
                                    borderBottom: '2px solid #f0f0f0'
                                },
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TabPane, {
                                        tab: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                            align: "center",
                                            gap: 6,
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UnorderedListOutlined, {}, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 610,
                                                    columnNumber: 17
                                                }, void 0),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                                    children: "全部任务"
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 611,
                                                    columnNumber: 17
                                                }, void 0)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 609,
                                            columnNumber: 15
                                        }, void 0)
                                    }, "all", false, {
                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                        lineNumber: 607,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TabPane, {
                                        tab: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                            align: "center",
                                            gap: 6,
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ClockCircleOutlined, {}, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 619,
                                                    columnNumber: 17
                                                }, void 0),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                                    children: "待处理"
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 620,
                                                    columnNumber: 17
                                                }, void 0)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 618,
                                            columnNumber: 15
                                        }, void 0)
                                    }, "pending", false, {
                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                        lineNumber: 616,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TabPane, {
                                        tab: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                            align: "center",
                                            gap: 6,
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckCircleOutlined, {}, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 628,
                                                    columnNumber: 17
                                                }, void 0),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                                    children: "已完成"
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 629,
                                                    columnNumber: 17
                                                }, void 0)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 627,
                                            columnNumber: 15
                                        }, void 0)
                                    }, "completed", false, {
                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                        lineNumber: 625,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                lineNumber: 592,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                            lineNumber: 591,
                            columnNumber: 7
                        }, this),
                        personalTasks.length > 0 && !loading && !error && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                marginBottom: 16,
                                padding: '12px 16px',
                                background: searchText.trim() ? filteredPersonalTasks.length > 0 ? 'linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%)' : 'linear-gradient(135deg, #fff2f0 0%, #ffccc7 100%)' : 'linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%)',
                                borderRadius: 8,
                                border: searchText.trim() ? filteredPersonalTasks.length > 0 ? '1px solid #91d5ff' : '1px solid #ff7875' : '1px solid #95de64',
                                boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)'
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                align: "center",
                                gap: 8,
                                children: searchText.trim() ? filteredPersonalTasks.length > 0 ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckCircleOutlined, {
                                            style: {
                                                color: '#1890ff',
                                                fontSize: 16
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 659,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            style: {
                                                fontSize: 14,
                                                color: '#1890ff'
                                            },
                                            children: [
                                                "找到 ",
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    strong: true,
                                                    style: {
                                                        color: '#1890ff',
                                                        fontSize: 16
                                                    },
                                                    children: filteredPersonalTasks.length
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 661,
                                                    columnNumber: 24
                                                }, this),
                                                " 条匹配的待办事项"
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 660,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {
                                            style: {
                                                color: '#ff4d4f',
                                                fontSize: 16
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 666,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            style: {
                                                fontSize: 14,
                                                color: '#ff4d4f'
                                            },
                                            children: "未找到匹配的待办事项，请尝试其他关键词"
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 667,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UnorderedListOutlined, {
                                            style: {
                                                color: '#52c41a',
                                                fontSize: 16
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 674,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            style: {
                                                fontSize: 14,
                                                color: '#52c41a'
                                            },
                                            children: [
                                                "共有 ",
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    strong: true,
                                                    style: {
                                                        color: '#52c41a',
                                                        fontSize: 16
                                                    },
                                                    children: filteredPersonalTasks.length
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 676,
                                                    columnNumber: 22
                                                }, this),
                                                " 条待办事项"
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 675,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                lineNumber: 655,
                                columnNumber: 11
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                            lineNumber: 639,
                            columnNumber: 9
                        }, this),
                        error ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                            message: "TODO数据加载失败",
                            description: error,
                            type: "error",
                            showIcon: true,
                            style: {
                                marginBottom: 16
                            }
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                            lineNumber: 686,
                            columnNumber: 9
                        }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                            spinning: loading,
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProList, {
                                    dataSource: filteredPersonalTasks,
                                    pagination: {
                                        current: pagination.current,
                                        pageSize: pagination.pageSize,
                                        total: filteredPersonalTasks.length,
                                        showSizeChanger: pagination.showSizeChanger,
                                        showQuickJumper: pagination.showQuickJumper,
                                        showTotal: pagination.showTotal,
                                        pageSizeOptions: pagination.pageSizeOptions,
                                        onChange: pagination.onChange,
                                        onShowSizeChange: pagination.onShowSizeChange
                                    },
                                    renderItem: (item)=>{
                                        return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            className: "todo-item",
                                            style: {
                                                padding: '10px 16px',
                                                marginBottom: 12,
                                                borderRadius: 8,
                                                background: '#fff',
                                                opacity: item.status === 1 ? 0.7 : 1,
                                                borderLeft: `3px solid ${item.status === 1 ? '#52c41a' : item.priority === 3 ? '#ff4d4f' : item.priority === 2 ? '#faad14' : '#8c8c8c'}`,
                                                boxShadow: '0 1px 4px rgba(0,0,0,0.05)'
                                            },
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                align: "center",
                                                gap: 12,
                                                style: {
                                                    width: '100%'
                                                },
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                        vertical: true,
                                                        align: "center",
                                                        children: [
                                                            item.status === 1 ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                align: "center",
                                                                justify: "center",
                                                                style: {
                                                                    width: 22,
                                                                    height: 22,
                                                                    borderRadius: '50%',
                                                                    background: '#52c41a'
                                                                },
                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckOutlined, {
                                                                    style: {
                                                                        color: '#fff',
                                                                        fontSize: 12
                                                                    }
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                    lineNumber: 744,
                                                                    columnNumber: 27
                                                                }, void 0)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                lineNumber: 734,
                                                                columnNumber: 25
                                                            }, void 0) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                style: {
                                                                    width: 18,
                                                                    height: 18,
                                                                    borderRadius: '50%',
                                                                    border: `2px solid ${item.priority === 3 ? '#ff4d4f' : item.priority === 2 ? '#faad14' : '#8c8c8c'}`
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                lineNumber: 749,
                                                                columnNumber: 25
                                                            }, void 0),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                style: {
                                                                    width: 2,
                                                                    height: 24,
                                                                    background: '#f0f0f0',
                                                                    marginTop: 4
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                lineNumber: 765,
                                                                columnNumber: 23
                                                            }, void 0)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 732,
                                                        columnNumber: 21
                                                    }, void 0),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                        vertical: true,
                                                        style: {
                                                            flex: 1
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                style: {
                                                                    fontSize: 14,
                                                                    fontWeight: item.priority === 3 ? 500 : 'normal',
                                                                    textDecoration: item.status === 1 ? 'line-through' : 'none',
                                                                    color: item.status === 1 ? '#8c8c8c' : '#262626'
                                                                },
                                                                children: item.title
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                lineNumber: 777,
                                                                columnNumber: 23
                                                            }, void 0),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                align: "center",
                                                                size: 6,
                                                                style: {
                                                                    marginTop: 4
                                                                },
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CalendarOutlined, {
                                                                        style: {
                                                                            fontSize: 12,
                                                                            color: '#8c8c8c'
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                        lineNumber: 791,
                                                                        columnNumber: 25
                                                                    }, void 0),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        type: "secondary",
                                                                        style: {
                                                                            fontSize: 12
                                                                        },
                                                                        children: [
                                                                            "创建于:",
                                                                            ' ',
                                                                            new Date(item.createdAt).toLocaleDateString('zh-CN')
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                        lineNumber: 797,
                                                                        columnNumber: 25
                                                                    }, void 0)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                lineNumber: 790,
                                                                columnNumber: 23
                                                            }, void 0)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 776,
                                                        columnNumber: 21
                                                    }, void 0),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Dropdown, {
                                                        trigger: [
                                                            'click'
                                                        ],
                                                        menu: {
                                                            items: [
                                                                {
                                                                    key: 'complete',
                                                                    label: item.status === 1 ? '标记未完成' : '标记完成',
                                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckOutlined, {
                                                                        style: {
                                                                            color: item.status === 1 ? '#8c8c8c' : '#52c41a',
                                                                            fontSize: 14
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                        lineNumber: 814,
                                                                        columnNumber: 31
                                                                    }, void 0)
                                                                },
                                                                {
                                                                    key: 'edit',
                                                                    label: '编辑任务',
                                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {
                                                                        style: {
                                                                            color: '#8c8c8c'
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                        lineNumber: 826,
                                                                        columnNumber: 35
                                                                    }, void 0)
                                                                },
                                                                {
                                                                    key: 'delete',
                                                                    label: '删除任务',
                                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {
                                                                        style: {
                                                                            color: '#ff4d4f'
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                        lineNumber: 832,
                                                                        columnNumber: 31
                                                                    }, void 0),
                                                                    danger: true
                                                                }
                                                            ],
                                                            onClick: ({ key })=>{
                                                                if (key === 'complete') handleToggleTodoStatus(item.id);
                                                                else if (key === 'edit') {
                                                                    setEditingTodoId(item.id);
                                                                    todoForm.setFieldsValue({
                                                                        name: item.title,
                                                                        priority: item.priority
                                                                    });
                                                                    setTodoModalVisible(true);
                                                                } else if (key === 'delete') handleDeleteTodo(item.id);
                                                            }
                                                        },
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                            type: "text",
                                                            size: "small",
                                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MoreOutlined, {}, void 0, false, {
                                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                lineNumber: 856,
                                                                columnNumber: 31
                                                            }, void 0),
                                                            style: {
                                                                width: 32,
                                                                height: 32
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                            lineNumber: 853,
                                                            columnNumber: 23
                                                        }, void 0)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 805,
                                                        columnNumber: 21
                                                    }, void 0)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                lineNumber: 730,
                                                columnNumber: 19
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 710,
                                            columnNumber: 17
                                        }, void 0);
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 695,
                                    columnNumber: 11
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ModalForm, {
                                    title: editingTodoId ? '编辑待办事项' : '新增待办事项',
                                    open: todoModalVisible,
                                    onOpenChange: (visible)=>{
                                        setTodoModalVisible(visible);
                                        if (!visible) {
                                            setEditingTodoId(null);
                                            todoForm.resetFields();
                                        }
                                    },
                                    form: todoForm,
                                    layout: "vertical",
                                    onFinish: handleAddOrUpdateTodo,
                                    autoComplete: "off",
                                    width: 500,
                                    modalProps: {
                                        centered: true,
                                        destroyOnClose: true,
                                        maskClosable: true,
                                        keyboard: true,
                                        forceRender: false
                                    },
                                    submitter: {
                                        searchConfig: {
                                            submitText: editingTodoId ? '更新任务' : '创建任务',
                                            resetText: '取消'
                                        },
                                        submitButtonProps: {
                                            style: {
                                                background: '#1890ff',
                                                borderColor: '#1890ff',
                                                boxShadow: '0 2px 4px rgba(24, 144, 255, 0.3)'
                                            },
                                            icon: editingTodoId ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                lineNumber: 900,
                                                columnNumber: 39
                                            }, void 0) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PlusOutlined, {}, void 0, false, {
                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                lineNumber: 900,
                                                columnNumber: 58
                                            }, void 0)
                                        },
                                        resetButtonProps: {
                                            style: {
                                                borderColor: '#d9d9d9'
                                            }
                                        },
                                        onReset: ()=>{
                                            setTodoModalVisible(false);
                                            setEditingTodoId(null);
                                            todoForm.resetFields();
                                        }
                                    },
                                    preserve: false,
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                            name: "name",
                                            label: "任务名称",
                                            rules: [
                                                {
                                                    required: true,
                                                    message: '请输入任务名称'
                                                }
                                            ],
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                                placeholder: "请输入任务名称",
                                                size: "large",
                                                style: {
                                                    borderRadius: 6
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                lineNumber: 920,
                                                columnNumber: 15
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 915,
                                            columnNumber: 13
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                            name: "priority",
                                            label: "优先级",
                                            initialValue: 2,
                                            rules: [
                                                {
                                                    required: true,
                                                    message: '请选择优先级'
                                                }
                                            ],
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                                                size: "large",
                                                options: [
                                                    {
                                                        value: 3,
                                                        label: '高优先级'
                                                    },
                                                    {
                                                        value: 2,
                                                        label: '中优先级'
                                                    },
                                                    {
                                                        value: 1,
                                                        label: '低优先级'
                                                    }
                                                ],
                                                style: {
                                                    borderRadius: 6
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                lineNumber: 933,
                                                columnNumber: 15
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 927,
                                            columnNumber: 13
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 867,
                                    columnNumber: 11
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                            lineNumber: 694,
                            columnNumber: 9
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                    lineNumber: 238,
                    columnNumber: 5
                }, this);
            };
            _s(TodoManagement, "1EvXb15TlcCEGOUvx3/r5LnZlFE=", false, function() {
                return [
                    _antd.Form.useForm,
                    _paginationUtils.usePagination
                ];
            });
            _c = TodoManagement;
            var _default = TodoManagement;
            var _c;
            $RefreshReg$(_c, "TodoManagement");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '6738038755728623953';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/settings/index.tsx": [
            "p__settings__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_personal-center_index_tsx-async.10566647384502541319.hot-update.js.map